package com.snct.device.service;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.redis.RedisCache;

import com.snct.dctcore.commoncore.analysis.BaseAnalysis;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.ModemHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.commoncore.snmp.SnmpRequest;
import com.snct.dctcore.commoncore.snmp.SnmpResponse;
import com.snct.dctcore.commoncore.snmp.SnmpUtils;
import com.snct.device.manager.DeviceConnectionManager;

import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.snct.dctcore.commoncore.enums.DeviceTypeEnum.MODEM;

/**
 * Modem设备服务
 * 管理Modem设备的通信和数据处理
 *
 * <AUTHOR>
 */
@Service
public class ModemDeviceService {

    @Autowired
    private StoreService storeService;

    private static final Logger logger = LoggerFactory.getLogger(ModemDeviceService.class);

    private static final String MODEM_DATA = CacheConstants.DEVICE_DATA_KEY + DeviceTypeEnum.MODEM.getAlias();

    // OID定义 - 去掉前导点
    private static final String OID_SIGNAL = "1.3.6.1.4.1.37576.4.2.1.21.1.4.1"; // 信号强度
    private static final String OID_SPEED = "1.3.6.1.4.1.37576.4.2.1.21.1.1.1"; // 速度
    private static final String OID_SEND_POWER = "1.3.6.1.4.1.37576.3.1.1.9.0"; // 发送功率
    private static final String OID_STATUS = "1.3.6.1.4.1.37576.4.2.1.2.0"; // 状态标志

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DeviceConnectionManager connectionManager;

    // 存储最近查询到的Modem数据
    private final List<ModemHbaseVo> modemDataList = new ArrayList<>();

    /**
     * 获取所有Modem设备数据
     * 从数据库查询所有Modem设备并执行SNMP查询
     */
    public void queryAllModemData() {
        // 从数据库中获取type为52的Modem设备
        Device queryDevice = new Device();
        queryDevice.setType(Long.valueOf(DeviceTypeEnum.MODEM.getValue()));
        queryDevice.setEnable(1);
        List<Device> modemDevices = deviceService.selectDeviceList(queryDevice);

        if (modemDevices == null || modemDevices.isEmpty()) {
            logger.info("未找到可用的Modem设备，跳过SNMP数据采集");
            return;
        }

        logger.debug("找到{}个Modem设备需要采集数据", modemDevices.size());

        // 清空数据列表
        modemDataList.clear();

        // 对每个设备执行SNMP查询
        for (Device modemDevice : modemDevices) {
            // 查询单个设备的SNMP数据
            ModemHbaseVo modemData = queryModemDevice(modemDevice);
            if (modemData != null) {
                modemDataList.add(modemData);
            }
        }
    }

    /**
     * 查询单个Modem设备的SNMP数据
     *
     * @param device 设备对象
     * @return Modem数据对象，查询失败返回null
     */
    private ModemHbaseVo queryModemDevice(Device device) {

        ModemHbaseVo modemHbaseVo = null;

        try {
            logger.debug("开始查询Modem设备[{}]的SNMP数据, IP:{}, 端口:{}",
                    device.getCode(), device.getIp(), device.getPort());

            // 创建SNMP请求，添加所需OID
            SnmpRequest request = new SnmpRequest()
                    .addOid(OID_SIGNAL, "信号强度")
                    .addOid(OID_SPEED, "速度")
                    .addOid(OID_SEND_POWER, "发送功率")
                    .addOid(OID_STATUS, "状态标志");

            // 执行SNMP请求
            SnmpResponse response = SnmpUtils.executeRequest(device, request);

            // 处理响应
            if (response != null && response.isSuccess()) {
                // 更新设备连接状态为连接
                connectionManager.updateDeviceConnectionStatus(device,
                        DeviceConnectionManager.ConnectionStatus.CONNECTED,
                        DeviceConnectionManager.ProtocolType.SNMP);

                KafkaMessage kafkaMessage = new KafkaMessage(MODEM.getValue(), device.getCode(),
                        JSONObject.toJSONString(response), 10, System.currentTimeMillis());

                Object analysisData = BaseAnalysis.analysisData(kafkaMessage, null, 1);
                if (analysisData != null) {
                    redisCache.setCacheObject(MODEM_DATA, analysisData);
                    modemHbaseVo = (ModemHbaseVo) analysisData;
                    kafkaMessage.setMsg(JSONObject.toJSONString(modemHbaseVo));
                    kafkaMessage.setSn(device.getSn());
                    storeService.savePackage2SendQueue(kafkaMessage);
                    storeService.storeDeviceData2MySql(kafkaMessage, device);

                    // 将消息保存到内存中，用于预览
                    String previewData = "信号强度:" + modemHbaseVo.getSignal() +
                            ", 速度:" + modemHbaseVo.getSpeed() +
                            ", 发送功率:" + modemHbaseVo.getSendPower() +
                            ", 状态标志:" + modemHbaseVo.getFlag();
                    storeService.savePreviewData(device, previewData);
                }

                return modemHbaseVo;
            } else {
                // 更新设备连接状态为断开
                connectionManager.updateDeviceConnectionStatus(device,
                        DeviceConnectionManager.ConnectionStatus.DISCONNECTED,
                        DeviceConnectionManager.ProtocolType.SNMP);

                logger.error("设备[{}]的SNMP查询失败: {}",
                        device.getCode(), response != null ? response.getErrorStatusText() : "无响应");
            }
        } catch (Exception e) {
            // 异常时更新设备连接状态为未知
            connectionManager.updateDeviceConnectionStatus(device,
                    DeviceConnectionManager.ConnectionStatus.UNKNOWN,
                    DeviceConnectionManager.ProtocolType.SNMP);

            logger.error("设备[{}]的Modem SNMP任务异常", device.getCode(), e);
        }
        return null;
    }


}
