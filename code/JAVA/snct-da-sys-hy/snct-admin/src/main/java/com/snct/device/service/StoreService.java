package com.snct.device.service;

import com.alibaba.fastjson2.JSONObject;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.*;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import com.snct.system.domain.Device;
import com.snct.system.domain.data.*;
import com.snct.system.service.*;
import com.snct.utils.DeviceRawDataFileUtil;
import com.snct.web.controller.business.DeviceController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @ClassName: StoreService
 * @Description: 存储服务
 * @author: wzewei
 * @date: 2025-09-09 13:57
 */
@Service
public class StoreService {

    private static final Logger logger = LoggerFactory.getLogger(StoreService.class);

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IBuDataGpsService buDataGpsService;

    @Autowired
    private IBuDataAwsService buDataAwsService;

    @Autowired
    private IBuDataPduService buDataPduService;

    @Autowired
    private IBuDataModemService buDataModemService;

    @Autowired
    private IBuDataAmplifierService buDataAmplifierService;

    @Autowired
    private IBuDataAttitudeService buDataAttitudeService;

    /**
     * 设备数据存储
     *
     * @param kafkaMessage Kafka消息对象
     * @param device       设备对象
     */
    public void storeDeviceData2MySql(KafkaMessage kafkaMessage, Device device) {
        if (kafkaMessage == null || device == null) {
            logger.warn("Kafka消息或设备对象为空，跳过数据库存储");
            return;
        }

        try {
            String msg = kafkaMessage.getMsg();
            if (msg == null || msg.isEmpty()) {
                logger.warn("Kafka消息内容为空，设备[{}]", device.getCode());
                return;
            }

            DeviceTypeEnum deviceType = DeviceTypeEnum.getByValue(Math.toIntExact(device.getType()));
            if (deviceType == null) {
                return;
            }
            switch (deviceType) {
                case GPS: // GPS设备
                    storeGpsData(kafkaMessage, device);
                    break;
                case AWS: // AWS气象设备
                    storeAwsData(kafkaMessage, device);
                    break;
                case PDU: // PDU设备
                    storePduData(kafkaMessage, device);
                    break;
                case MODEM: // 卫星猫设备
                    storeModemData(kafkaMessage, device);
                    break;
                case AMPLIFIER: // 功放设备
                    storeAmplifierData(kafkaMessage, device);
                    break;
                case ATTITUDE: // 姿态仪设备
                    storeAttitudeData(kafkaMessage, device);
                    break;
                default:
                    logger.warn("未支持的设备类型: {}，设备[{}]", deviceType, device.getCode());
            }
        } catch (Exception e) {
            logger.error("保存设备数据到数据库失败，设备[{}]", device.getCode(), e);
        }
    }

    /**
     * 设备原始数据存储
     *
     * @param kafkaMessage Kafka消息对象
     * @param device       设备对象
     */
    public void storeRawData2File(Device device, String msg, Long initialTime) {
        DeviceRawDataFileUtil.saveRawDataToFile(device, msg, initialTime);
    }

    /**
     * 保存数据包到发送队列
     * 将Kafka消息转换为TransferPackage并保存到Redis队列中，供后续发送使用
     *
     * @param kafkaMessage Kafka消息对象
     */
    public void savePackage2SendQueue(KafkaMessage kafkaMessage) {
        if (kafkaMessage == null) {
            logger.warn("Kafka消息为空，跳过保存到发送队列");
            return;
        }

        try {
            // 生成记录编号（当前时间戳的秒数）
            Long recordNum = System.currentTimeMillis() / 1000;

            // 创建传输包对象
            TransferPackage transferPackage = new TransferPackage(recordNum, kafkaMessage.getInitialTime(),
                    PackageTypeEnum.DEVICE_DATA.getValue(), 0, kafkaMessage.getMsg(),
                    kafkaMessage.getType(), kafkaMessage.getCode());
            transferPackage.setSn(kafkaMessage.getSn());

            // 将命令编号添加到修复数据集合中
            SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();
            opsForSet.add(RedisParameter.SHIP_REPAIR_DATA_SET, transferPackage.getCommandNum());

            // 将传输包保存到Redis发送队列中
            ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();
            String key = RedisParameter.SHIP_DO_SEND_LIST_COST + kafkaMessage.getCost();
            opsForList.rightPush(key, transferPackage);

            logger.debug("成功保存数据包到发送队列，设备[{}]，命令编号[{}]",
                    kafkaMessage.getCode(), transferPackage.getCommandNum());
        } catch (Exception e) {
            logger.error("保存数据包到发送队列失败，设备[{}]", kafkaMessage.getCode(), e);
        }
    }

    /**
     * 保存设备数据到内存中用于预览
     *
     * @param device  设备实体
     * @param message 原始消息
     */
    public void savePreviewData(Device device, String message) {
        String deviceCode = device != null ? device.getCode() : null;
        try {
            if (deviceCode != null && !deviceCode.isEmpty()) {
                if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(deviceCode)) {
                    LocalDateTime now = LocalDateTime.now();
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                    String formattedTime = now.format(formatter);
                    String key = deviceCode + "###" + formattedTime;
                    DeviceController.viewValue.put(key, message);
                }
            }
        } catch (Exception e) {
            logger.error("保存[{}]预览数据异常", deviceCode, e);
        }
    }



    // =======================================================================================

    /**
     * 存储GPS数据
     */
    private void storeGpsData(KafkaMessage kafkaMessage, Device device) {
        // 从msg字段解析GPS数据对象
        GpsHbaseVo gpsHbaseVo = JSONObject.parseObject(kafkaMessage.getMsg(), GpsHbaseVo.class);

        BuDataGps entity = new BuDataGps();
        BeanUtils.copyProperties(gpsHbaseVo, entity);
        setEntityInfo(entity, device, kafkaMessage);
        buDataGpsService.insertBuDataGps(entity);

    }

    /**
     * 存储AWS气象数据
     */
    private void storeAwsData(KafkaMessage kafkaMessage, Device device) {
        AwsHbaseVo awsHbaseVo = JSONObject.parseObject(kafkaMessage.getMsg(), AwsHbaseVo.class);
        BuDataAws entity = new BuDataAws();
        BeanUtils.copyProperties(awsHbaseVo, entity);
        setEntityInfo(entity, device, kafkaMessage);
        buDataAwsService.insertBuDataAws(entity);
    }

    /**
     * 存储PDU数据
     */
    private void storePduData(KafkaMessage kafkaMessage, Device device) {
        PduHbaseVo pduHbaseVo = JSONObject.parseObject(kafkaMessage.getMsg(), PduHbaseVo.class);
        BuDataPdu entity = new BuDataPdu();
        BeanUtils.copyProperties(pduHbaseVo, entity);
        setEntityInfo(entity, device, kafkaMessage);
        buDataPduService.insertBuDataPdu(entity);
    }

    /**
     * 存储卫星猫数据
     */
    private void storeModemData(KafkaMessage kafkaMessage, Device device) {
        ModemHbaseVo modemHbaseVo = JSONObject.parseObject(kafkaMessage.getMsg(), ModemHbaseVo.class);
        BuDataModem entity = new BuDataModem();
        BeanUtils.copyProperties(modemHbaseVo, entity);
        setEntityInfo(entity, device, kafkaMessage);
        buDataModemService.insertBuDataModem(entity);
    }

    /**
     * 存储功放数据
     */
    private void storeAmplifierData(KafkaMessage kafkaMessage, Device device) {
        AmplifierHbaseVo amplifierHbaseVo = JSONObject.parseObject(kafkaMessage.getMsg(), AmplifierHbaseVo.class);
        BuDataAmplifier entity = new BuDataAmplifier();
        BeanUtils.copyProperties(amplifierHbaseVo, entity);
        setEntityInfo(entity, device, kafkaMessage);
        buDataAmplifierService.insertBuDataAmplifier(entity);
    }

    /**
     * 存储姿态仪数据
     */
    private void storeAttitudeData(KafkaMessage kafkaMessage, Device device) {
        AttitudeHbaseVo attitudeHbaseVo = JSONObject.parseObject(kafkaMessage.getMsg(), AttitudeHbaseVo.class);
        BuDataAttitude entity = new BuDataAttitude();
        BeanUtils.copyProperties(attitudeHbaseVo, entity);
        setEntityInfo(entity, device, kafkaMessage);
        buDataAttitudeService.insertBuDataAttitude(entity);
    }

    /**
     * 设置实体信息（设备信息 + 时间信息）
     */
    private void setEntityInfo(Object entity, Device device, KafkaMessage kafkaMessage) {
        try {
            // 设置设备信息
            entity.getClass().getMethod("setSn", String.class).invoke(entity, device.getSn());
            entity.getClass().getMethod("setDeviceId", Long.class).invoke(entity, device.getId());
            entity.getClass().getMethod("setDeviceCode", String.class).invoke(entity, device.getCode());

            // 设置时间信息
            Long initialTime = kafkaMessage.getInitialTime();
            if (initialTime != null) {
                entity.getClass().getMethod("setInitialTime", Long.class).invoke(entity, initialTime);
                entity.getClass().getMethod("setInitialBjTime", Date.class).invoke(entity, new Date(initialTime));
            }
        } catch (Exception e) {
            logger.debug("设置实体信息失败", e);
        }
    }

}
