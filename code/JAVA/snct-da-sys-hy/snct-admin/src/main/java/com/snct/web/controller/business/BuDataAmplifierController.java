package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.data.BuDataAmplifier;
import com.snct.system.service.IBuDataAmplifierService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 功放数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/system/amplifier")
public class BuDataAmplifierController extends BaseController
{
    @Autowired
    private IBuDataAmplifierService buDataAmplifierService;

    /**
     * 查询功放数据列表
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:amplifier:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDataAmplifier buDataAmplifier)
    {
        startPage();
        List<BuDataAmplifier> list = buDataAmplifierService.selectBuDataAmplifierList(buDataAmplifier);
        return getDataTable(list);
    }

    /**
     * 导出功放数据列表
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:amplifier:export')")
    @Log(title = "功放数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDataAmplifier buDataAmplifier)
    {
        List<BuDataAmplifier> list = buDataAmplifierService.selectBuDataAmplifierList(buDataAmplifier);
        ExcelUtil<BuDataAmplifier> util = new ExcelUtil<BuDataAmplifier>(BuDataAmplifier.class);
        util.exportExcel(response, list, "功放数据数据");
    }

    /**
     * 获取功放数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:amplifier:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDataAmplifierService.selectBuDataAmplifierById(id));
    }

    /**
     * 新增功放数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:amplifier:add')")
    @Log(title = "功放数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDataAmplifier buDataAmplifier)
    {
        return toAjax(buDataAmplifierService.insertBuDataAmplifier(buDataAmplifier));
    }

    /**
     * 修改功放数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:amplifier:edit')")
    @Log(title = "功放数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDataAmplifier buDataAmplifier)
    {
        return toAjax(buDataAmplifierService.updateBuDataAmplifier(buDataAmplifier));
    }

    /**
     * 删除功放数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:amplifier:remove')")
    @Log(title = "功放数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDataAmplifierService.deleteBuDataAmplifierByIds(ids));
    }
}
