package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.data.BuDataAttitude;
import com.snct.system.service.IBuDataAttitudeService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 姿态仪数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/system/attitude")
public class BuDataAttitudeController extends BaseController
{
    @Autowired
    private IBuDataAttitudeService buDataAttitudeService;

    /**
     * 查询姿态仪数据列表
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:attitude:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDataAttitude buDataAttitude)
    {
        startPage();
        List<BuDataAttitude> list = buDataAttitudeService.selectBuDataAttitudeList(buDataAttitude);
        return getDataTable(list);
    }

    /**
     * 导出姿态仪数据列表
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:attitude:export')")
    @Log(title = "姿态仪数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDataAttitude buDataAttitude)
    {
        List<BuDataAttitude> list = buDataAttitudeService.selectBuDataAttitudeList(buDataAttitude);
        ExcelUtil<BuDataAttitude> util = new ExcelUtil<BuDataAttitude>(BuDataAttitude.class);
        util.exportExcel(response, list, "姿态仪数据数据");
    }

    /**
     * 获取姿态仪数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:attitude:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDataAttitudeService.selectBuDataAttitudeById(id));
    }

    /**
     * 新增姿态仪数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:attitude:add')")
    @Log(title = "姿态仪数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDataAttitude buDataAttitude)
    {
        return toAjax(buDataAttitudeService.insertBuDataAttitude(buDataAttitude));
    }

    /**
     * 修改姿态仪数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:attitude:edit')")
    @Log(title = "姿态仪数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDataAttitude buDataAttitude)
    {
        return toAjax(buDataAttitudeService.updateBuDataAttitude(buDataAttitude));
    }

    /**
     * 删除姿态仪数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:attitude:remove')")
    @Log(title = "姿态仪数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDataAttitudeService.deleteBuDataAttitudeByIds(ids));
    }
}
