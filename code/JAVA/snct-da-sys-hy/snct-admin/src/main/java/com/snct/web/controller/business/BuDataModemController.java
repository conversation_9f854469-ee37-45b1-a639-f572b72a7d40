package com.snct.web.controller.business;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.data.BuDataModem;
import com.snct.system.service.IBuDataModemService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 卫星猫数据Controller
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@RestController
@RequestMapping("/system/modem")
public class BuDataModemController extends BaseController
{
    @Autowired
    private IBuDataModemService buDataModemService;

    /**
     * 查询卫星猫数据列表
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:modem:list')")
    @GetMapping("/list")
    public TableDataInfo list(BuDataModem buDataModem)
    {
        startPage();
        List<BuDataModem> list = buDataModemService.selectBuDataModemList(buDataModem);
        return getDataTable(list);
    }

    /**
     * 导出卫星猫数据列表
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:modem:export')")
    @Log(title = "卫星猫数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BuDataModem buDataModem)
    {
        List<BuDataModem> list = buDataModemService.selectBuDataModemList(buDataModem);
        ExcelUtil<BuDataModem> util = new ExcelUtil<BuDataModem>(BuDataModem.class);
        util.exportExcel(response, list, "卫星猫数据数据");
    }

    /**
     * 获取卫星猫数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:modem:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(buDataModemService.selectBuDataModemById(id));
    }

    /**
     * 新增卫星猫数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:modem:add')")
    @Log(title = "卫星猫数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BuDataModem buDataModem)
    {
        return toAjax(buDataModemService.insertBuDataModem(buDataModem));
    }

    /**
     * 修改卫星猫数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:modem:edit')")
    @Log(title = "卫星猫数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BuDataModem buDataModem)
    {
        return toAjax(buDataModemService.updateBuDataModem(buDataModem));
    }

    /**
     * 删除卫星猫数据
     */
    @PreAuthorize("@ss.hasPermi('business:devicedata:modem:remove')")
    @Log(title = "卫星猫数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(buDataModemService.deleteBuDataModemByIds(ids));
    }
}
