package com.snct.web.controller.api;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.annotation.Anonymous;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.utils.StringUtils;
import com.snct.common.utils.ip.IpUtils;
import com.snct.dctcore.commoncore.analysis.BaseAnalysis;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.AttitudeHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceCategoryEnum;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.device.manager.DeviceConnectionManager;
import com.snct.device.service.StoreService;
import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import com.snct.web.controller.api.dto.DeviceDataRequest;
import com.snct.web.controller.api.dto.DeviceLoginRequest;
import com.snct.web.controller.api.dto.DeviceLoginResponse;
import com.snct.web.controller.api.dto.DeviceTokenInfo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 设备数据传输API控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/device")
public class ImportDataController {
    private static final Logger log = LoggerFactory.getLogger(ImportDataController.class);

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private DeviceConnectionManager connectionManager;

    /**
     * 设备认证固定账号
     */
    private static final String DEVICE_ACCOUNT = "attitude";

    /**
     * 设备认证固定密码
     */
    private static final String DEVICE_PASSWORD = "Aa123456#";

    /**
     * Token有效期（8小时，单位：秒）
     */
    private static final int TOKEN_EXPIRE_TIME = 8 * 60 * 60;

    /**
     * JWT密钥
     */
    @Value("${token.secret}")
    private String jwtSecret;

    private static final String ATTITUDE_DATA = CacheConstants.DEVICE_DATA_KEY + "attitude";

    /**
     * 姿态设备固定编码
     */
    private static final String ATTITUDE_DEVICE_CODE = "033A";

    /**
     * 设备登录认证接口
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @Anonymous
    @PostMapping("/loginApi")
    public AjaxResult deviceLogin(@Validated @RequestBody DeviceLoginRequest request, HttpServletRequest httpRequest) {
        try {

            // 验证设备账号和密码
            if (!DEVICE_ACCOUNT.equals(request.getDeviceAccount()) ||
                    !DEVICE_PASSWORD.equals(request.getDevicePassword())) {
                log.warn("设备登录失败，账号或密码错误: {}", request.getDeviceAccount());
                return AjaxResult.error("设备账号或密码错误");
            }

            // 生成token
            String token = generateDeviceToken(request.getDeviceAccount());

            // 计算过期时间
            long currentTime = System.currentTimeMillis();
            long expireTime = currentTime + TOKEN_EXPIRE_TIME * 1000L;

            // 获取客户端IP
            String ipAddress = IpUtils.getIpAddr(httpRequest);

            // 创建token信息
            DeviceTokenInfo tokenInfo = new DeviceTokenInfo(
                    request.getDeviceAccount(),
                    currentTime,
                    expireTime,
                    ipAddress
            );

            // 存储到Redis
            String tokenKey = CacheConstants.DEVICE_TOKEN_KEY + token;
            redisCache.setCacheObject(tokenKey, tokenInfo, TOKEN_EXPIRE_TIME, TimeUnit.SECONDS);

            // 构建响应
            DeviceLoginResponse response = new DeviceLoginResponse(token, expireTime, TOKEN_EXPIRE_TIME);

            //log.info("设备登录成功，账号: {}, token: {}, 过期时间: {}", request.getDeviceAccount(), token, expireTime);
            log.info("姿态登录成功>: {}", request.getDeviceAccount());

            return AjaxResult.success( "登录成功", response);
        } catch (Exception e) {
            log.error("设备登录异常", e);
            return AjaxResult.error("登录失败，系统异常");
        }
    }

    /**
     * 设备数据接收接口
     *
     * @param request     姿态数据请求
     * @param httpRequest HTTP请求
     * @return 处理结果
     */
    @Anonymous
    @PostMapping("/importData")
    public AjaxResult receiveAttitudeData(@Validated @RequestBody DeviceDataRequest request,
                                          HttpServletRequest httpRequest) {
        try {
            log.info("接收姿态设备[{}]数据请求: {}", ATTITUDE_DEVICE_CODE, request);

            // 获取并验证token
            String token = getTokenFromRequest(httpRequest);
            if (StringUtils.isEmpty(token)) {
                updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED, "缺少token");
                log.warn("数据接收失败，缺少token");
                return AjaxResult.error(401, "缺少访问令牌");
            }

            // 验证token有效性
            DeviceTokenInfo tokenInfo = validateDeviceToken(token);
            if (tokenInfo == null) {
                updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED, "token无效");
                log.warn("数据接收失败，token无效: {}", token);
                return AjaxResult.error(401, "访问令牌无效");
            }

            if (tokenInfo.isExpired()) {
                updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED, "token过期");
                log.warn("数据接收失败，token已过期: {}", token);
                return AjaxResult.error(401, "访问令牌已过期");
            }

            // 设置数据时间戳（如果未提供）
            if (request.getTimestamp() == null) {
                request.setTimestamp(System.currentTimeMillis());
            }

            // 处理姿态数据
            Map<String, Object> properties = request.getProperties();
            long currentTime = request.getTimestamp() == null ? System.currentTimeMillis() :
                    request.getTimestamp();
            String message = JSONObject.toJSONString(properties);
            Device device = getDeviceByCode(ATTITUDE_DEVICE_CODE);

            // 保存原数据
            storeService.storeRawData2File(device, message, request.getTimestamp());
            // 保存预览数据
            storeService.savePreviewData(device, message);

            KafkaMessage kafkaMessage = new KafkaMessage(DeviceTypeEnum.ATTITUDE.getValue(), device.getCode(), message, 10, currentTime);
            Object analysisData = BaseAnalysis.analysisData(kafkaMessage, null, DeviceCategoryEnum.TYPE_1.getValue());

            if (analysisData != null) {
                redisCache.setCacheObject(ATTITUDE_DATA, analysisData);
                AttitudeHbaseVo buDataAttitude = (AttitudeHbaseVo) analysisData;
                kafkaMessage.setMsg(JSONObject.toJSONString(buDataAttitude));
                kafkaMessage.setSn(device.getSn());
                storeService.savePackage2SendQueue(kafkaMessage);
                storeService.storeDeviceData2MySql(kafkaMessage, device);
            }

            // 更新设备连接状态为连接
            updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                DeviceConnectionManager.ConnectionStatus.CONNECTED, "数据接收成功");

            //log.info("设备[{}]数据接收成功，存储到Redis key: {}", ATTITUDE_DEVICE_CODE, ATTITUDE_DATA);
            return AjaxResult.success("姿态数据接收成功");
        } catch (Exception e) {
            updateDeviceConnectionStatus(ATTITUDE_DEVICE_CODE,
                DeviceConnectionManager.ConnectionStatus.UNKNOWN, "系统异常: " + e.getMessage());
            log.error("设备[{}]数据接收异常", ATTITUDE_DEVICE_CODE, e);
            return AjaxResult.error("姿态数据接收失败，系统异常");
        }
    }

    /**
     * 查询设备数据接口（用于调试）
     *
     * @param httpRequest HTTP请求
     * @return 设备数据
     */
    @Anonymous
    @GetMapping("/data")
    public AjaxResult getDeviceData(HttpServletRequest httpRequest) {
        try {
            // 获取并验证token
            String token = getTokenFromRequest(httpRequest);
            if (StringUtils.isEmpty(token)) {
                return AjaxResult.error(401, "缺少访问令牌");
            }

            DeviceTokenInfo tokenInfo = validateDeviceToken(token);
            if (tokenInfo == null || tokenInfo.isExpired()) {
                return AjaxResult.error(401, "访问令牌无效或已过期");
            }

            // 获取姿态数据
            Object attitudeData = redisCache.getCacheObject(ATTITUDE_DATA);

            if (attitudeData == null) {
                return AjaxResult.error("数据不存在");
            }

            return AjaxResult.success("查询成功", attitudeData);
        } catch (Exception e) {
            log.error("查询数据异常", e);
            return AjaxResult.error("查询失败，系统异常");
        }
    }

    /**
     * 生成设备Token
     *
     * @param deviceAccount 设备账号
     * @return JWT Token
     */
    private String generateDeviceToken(String deviceAccount) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("deviceAccount", deviceAccount);
        claims.put("tokenType", "device");
        claims.put("createTime", System.currentTimeMillis());

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(deviceAccount)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    /**
     * 从请求中获取Token
     *
     * @param request HTTP请求
     * @return Token字符串
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 优先从Header中获取
        String token = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(token)) {
            if (token.startsWith("Bearer ")) {
                return token.substring(7);
            }
            return token;
        }

        // 从参数中获取
        token = request.getParameter("token");
        return token;
    }

    /**
     * 验证设备Token
     *
     * @param token Token字符串
     * @return Token信息，如果无效返回null
     */
    private DeviceTokenInfo validateDeviceToken(String token) {
        try {
            // 验证JWT格式和签名
            Claims claims = Jwts.parser()
                    .setSigningKey(jwtSecret)
                    .parseClaimsJws(token)
                    .getBody();

            // 检查token类型
            String tokenType = (String) claims.get("tokenType");
            if (!"device".equals(tokenType)) {
                log.warn("Token类型不匹配: {}", tokenType);
                return null;
            }

            // 从Redis中获取token信息
            String tokenKey = CacheConstants.DEVICE_TOKEN_KEY + token;
            DeviceTokenInfo tokenInfo = redisCache.getCacheObject(tokenKey);

            if (tokenInfo == null) {
                log.warn("Token在Redis中不存在: {}", token);
                return null;
            }

            return tokenInfo;
        } catch (Exception e) {
            log.warn("Token验证失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 更新设备连接状态的辅助方法
     *
     * @param deviceCode 设备编码
     * @param status 连接状态
     * @param reason 状态变更原因
     */
    private void updateDeviceConnectionStatus(String deviceCode,
                                            DeviceConnectionManager.ConnectionStatus status,
                                            String reason) {
        try {
            Device device = getDeviceByCode(deviceCode);
            if (device != null) {
                connectionManager.updateDeviceConnectionStatus(device, status,
                    DeviceConnectionManager.ProtocolType.HTTP);
                log.info("设备[{}]连接状态更新: {} - {}", deviceCode, status.getDescription(), reason);
            }
        } catch (Exception e) {
            log.error("更新设备[{}]连接状态失败", deviceCode, e);
        }
    }

    /**
     * 根据设备编码获取设备对象
     *
     * @param deviceCode 设备编码
     * @return 设备对象
     */
    private Device getDeviceByCode(String deviceCode) {
        try {
            Device query = new Device();
            query.setCode(deviceCode);
            query.setEnable(1);
            List<Device> devices = deviceService.selectDeviceList(query);

            if (devices != null && !devices.isEmpty()) {
                return devices.get(0);
            }
        } catch (Exception e) {
            log.error("根据设备编码[{}]查询设备失败", deviceCode, e);
        }
        return null;
    }


}

