package com.snct.netty;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.snct.common.core.redis.RedisCache;

import com.snct.common.utils.StringUtils;
import com.snct.common.utils.spring.SpringUtils;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import com.snct.dctcore.commoncore.utils.NumUtils;
import com.snct.system.service.ISysConfigService;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * 数据发送服务类
 * 主要功能：
 * 1. 从Redis队列中消费数据包并通过UDP发送到LAPUTA系统
 * 2. 维持与LAPUTA系统的连接心跳
 * 3. 实现流量控制和数据包重发机制
 * 4. 支持不同类型数据包的格式化和发送
 */
@Service
public class SendService {
    private final Logger logger = LoggerFactory.getLogger(SendService.class);

    @Autowired
    private RedisCache redisCache;

    /**
     * 设备序列号，用于数据包标识
     */
    private String sn;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ISysConfigService configService;

    /**
     * 发送参数映射表，用于流量控制统计
     */
    private static final ConcurrentMap<String, Integer> sendParametersMap = new ConcurrentHashMap<>();

    /**
     * 连接保持标志，防止重复启动心跳线程
     */
    private static boolean isSendConnectKeep = false;

    /**
     * LAPUTA系统默认IP地址
     */
    private static String laputaIp = "*************";

    /**
     * LAPUTA系统默认端口号
     */
    private static Integer laputaPort = 28001;

    /**
     * 更新LAPUTA系统的IP地址和端口号
     * 从系统配置中读取最新的连接参数
     */
    public void renewLaputaIpPort() {
        // 从系统配置表中获取LAPUTA系统的IP和端口配置
        String ip = configService.selectConfigByKey("sys.laputa.ip");
        String port = configService.selectConfigByKey("sys.laputa.port");

        // 验证配置参数的有效性
        if (StringUtils.isEmpty(ip) || StringUtils.isEmpty(port)) {
            logger.error("更新LAPUTA的IP和PORT失败,--{}--{}", ip, port);
            return;
        }

        // 更新静态变量中的连接参数
        laputaIp = ip;
        laputaPort = Integer.valueOf(port);
        logger.info("更新LAPUTA的IP和PORT成功,--{}--{}", laputaIp, laputaPort);
    }

    /**
     * 消费Redis队列中的数据包并发送
     * 这是服务的主要入口方法，启动UDP客户端并持续处理数据发送
     */
    public void consumeRedisList() {
        // 获取线程池执行器，用于异步执行发送任务
        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            // 创建Netty的NIO事件循环组，用于处理网络IO
            NioEventLoopGroup nioEventLoopGroup = new NioEventLoopGroup();
            try {
                try {
                    // 配置Netty UDP客户端启动器
                    Bootstrap bootstrap = new Bootstrap();
                    bootstrap.group(nioEventLoopGroup)
                            .channel(NioDatagramChannel.class)  // 使用UDP数据报通道
                            .option(ChannelOption.SO_BROADCAST, true)  // 启用广播选项
                            .handler(new NormalUdpClientHandler());  // 设置消息处理器

                    // 绑定本地端口10001并同步等待绑定完成
                    ChannelFuture channel = bootstrap.bind(10001).sync();

                    // 获取Redis操作模板，用于不同数据结构的操作
                    ListOperations<String, TransferPackage> opsForList = redisTemplate.opsForList();
                    ValueOperations<String, Integer> valueOperations = redisTemplate.opsForValue();
                    SetOperations<String, Integer> opsForSet = redisTemplate.opsForSet();
                    ValueOperations<String, TransferPackage> opsForValue = redisTemplate.opsForValue();

                    // 定义不同优先级的消费成本列表，数字越小优先级越高
                    List<Integer> costList = Lists.newArrayList(10, 20, 30, 40, 50);

                    // 启动连接保持机制
                    keepConnect(channel);

                    // 主循环：持续从Redis队列中消费并发送数据
                    while (true) {
                        sendMessage(channel, opsForList, valueOperations, opsForSet, opsForValue, costList);
                    }
                } catch (Exception e) {
                    logger.error("异常", e);
                    // 异常时优雅关闭事件循环组并重新启动服务
                    nioEventLoopGroup.shutdownGracefully();
                    consumeRedisList();
                }
            } catch (Throwable th) {
                // 确保在任何异常情况下都能正确关闭资源
                nioEventLoopGroup.shutdownGracefully();
                consumeRedisList();
                throw th;
            }
        });
    }

    /**
     * 维持与LAPUTA系统的连接
     * 定期发送心跳包以保持连接活跃状态
     *
     * @param udpChannel UDP通道
     */
    private void keepConnect(ChannelFuture udpChannel) {
        // 防止重复启动连接保持线程
        if (isSendConnectKeep) {
            return;
        }

        // 使用线程池异步执行连接保持任务
        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            // 设置连接保持标志，防止重复启动
            isSendConnectKeep = true;

            // 持续发送心跳包
            while (true) {
                try {
                    // 更新LAPUTA系统的连接参数
                    renewLaputaIpPort();
                    // 发送连接保持消息
                    sendConnectKeepMessage(udpChannel);
                    // 每10秒发送一次心跳包
                    Thread.sleep(10000L);
                } catch (InterruptedException e) {
                    logger.error("ConnectKeep-出错---{}", e);
                }
            }
        });
    }

    /**
     * 发送消息的核心方法
     * 按优先级从Redis队列中取出数据包并发送，同时进行流量控制
     *
     * @param udpChannel      UDP通道
     * @param opsForList      Redis列表操作
     * @param valueOperations Redis值操作
     * @param opsForSet       Redis集合操作
     * @param opsForValue     Redis值操作（TransferPackage类型）
     * @param costList        优先级成本列表
     * @throws Exception 发送异常
     */
    private void sendMessage(ChannelFuture udpChannel, ListOperations<String, TransferPackage> opsForList,
                             ValueOperations<String, Integer> valueOperations, SetOperations<String, Integer> opsForSet,
                             ValueOperations<String, TransferPackage> opsForValue, List<Integer> costList) throws Exception {
        TransferPackage transferPackage;

        // 从Redis获取带宽限制配置，默认2000字节
        Integer maxLength = valueOperations.get(RedisParameter.PAZU_BANDWIDTH_LENGTH) == null ?
                Integer.valueOf(2000) : valueOperations.get(RedisParameter.PAZU_BANDWIDTH_LENGTH);

        // 从Redis获取单次发送总量配置，默认100
        Integer sendTotal = valueOperations.get(RedisParameter.PAZU_SEND_TOTAL_ONE_TIME) == null ?
                Integer.valueOf(100) : valueOperations.get(RedisParameter.PAZU_SEND_TOTAL_ONE_TIME);

        // 按优先级处理不同成本的队列
        for (Integer cost : costList) {
            // 构造Redis队列键名
            String key = RedisParameter.SHIP_DO_SEND_LIST_COST + cost;
            int num = 0;

            // 计算当前优先级可发送的数量
            int canSendNum = 0;
            if (sendTotal != null) {
                canSendNum = sendTotal / cost;
            }

            // 最高优先级（cost=10）特殊处理，可发送更多数据
            if (cost == 10) {
                canSendNum = 200;
            }

            // 从队列左侧弹出数据包并发送，直到达到发送限制或队列为空
            while (num < canSendNum && (transferPackage = opsForList.leftPop(key)) != null) {
                // 根据消息长度进行流量控制延时
                if (maxLength != null) {
                    needSleep(transferPackage.getMessage(), maxLength);
                }

                // 根据数据包类型进行不同的处理
                if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
                    sendDataMessage(udpChannel, transferPackage, opsForSet, opsForValue);
                }

                num++;
                logger.info("已发送一条UDP数据----{}", JSONObject.toJSONString(transferPackage));
            }
        }
    }

    /**
     * 流量控制方法
     * 根据已发送数据长度和时间进行动态延时，防止网络拥塞
     *
     * @param message   当前要发送的消息
     * @param maxLength 最大带宽限制
     * @throws InterruptedException 线程中断异常
     */
    private void needSleep(String message, Integer maxLength) throws InterruptedException {
        // 获取当前消息长度
        int currentLength = message.length();

        // 从参数映射表中获取已发送数据长度，默认为0
        int hasSendLength = sendParametersMap.get("hasSendLength") == null ?
                0 : sendParametersMap.get("hasSendLength");

        // 从参数映射表中获取已用时间，默认为0
        int hasTime = sendParametersMap.get("hasTime") == null ?
                0 : sendParametersMap.get("hasTime");

        // 判断是否需要进行流量控制
        if (currentLength + hasSendLength < maxLength && hasTime < 1000) {
            // 未达到带宽限制且时间窗口未满，累加发送长度和时间
            sendParametersMap.put("hasSendLength", currentLength + hasSendLength);
            sendParametersMap.put("hasTime", hasTime + RedisParameter.DEFAULT_SLEEP_TIME);
            // 按默认间隔休眠
            Thread.sleep(RedisParameter.DEFAULT_SLEEP_TIME);
        } else {
            // 达到限制，清空统计参数并等待时间窗口结束
            cleanSendParametersMap();
            // 计算剩余等待时间，确保每秒的发送量不超过限制
            int t = 1000 - hasTime > 0 ? 1000 - hasTime : 1;
            Thread.sleep(t);
        }
    }

    private void sendDataMessage(ChannelFuture udpChannel, TransferPackage transferPackage, SetOperations<String,
            Integer> opsForSet, ValueOperations<String, TransferPackage> opsForValue) {
        byte[] udpMsg = dataFormat(transferPackage);
        udpChannel.channel().writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer(udpMsg),
                new InetSocketAddress(laputaIp, laputaPort)));
        opsForSet.remove(RedisParameter.SHIP_REPAIR_DATA_SET, transferPackage.getCommandNum());
        if (transferPackage.getIsRepair() == 1) {
            logger.info("补发送成功。。{}", udpMsg);
        } else {
            logger.info("UDP发送成功。。{}", udpMsg);
            //saveDeviceData2Hash(transferPackage, opsForValue);
        }
    }

    private void saveDeviceData2Hash(TransferPackage transferPackage,
                                     ValueOperations<String, TransferPackage> opsForValue) {
        String key = RedisParameter.SHIP_DEVICE_HASH_DATA + transferPackage.getCommandNum();
        opsForValue.set(key, transferPackage, 48L, TimeUnit.HOURS);
    }

    private void sendPackageMessage(ChannelFuture udpChannel, TransferPackage transferPackage, ValueOperations<String
            , TransferPackage> opsForValue, SetOperations<String, Integer> opsForSet) {
        byte[] message = dataFormat(transferPackage);
        if (transferPackage.getIsRepair() == 0) {
            savePackageData2Hash(transferPackage, opsForValue);
        }
        udpChannel.channel().writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer(message),
                new InetSocketAddress(laputaIp, laputaPort)));
        opsForSet.remove(RedisParameter.SHIP_REPAIR_PACKAGE_DATA_SET + transferPackage.getDeviceCode(),
                transferPackage.getUnpackingNum());
        if (transferPackage.getIsRepair() == 0) {
            logger.info("发送拆包数据成功。。");
        } else {
            logger.info("拆包数据补发成功。。");
        }
    }

    private void savePackageData2Hash(TransferPackage transferPackage,
                                      ValueOperations<String, TransferPackage> opsForValue) {
        String key =
                RedisParameter.SHIP_PACKAGE_DATA + transferPackage.getCommandNum() + "_" + transferPackage.getDeviceCode() + "_" + transferPackage.getUnpackingNum();
        opsForValue.set(key, transferPackage, 40L, TimeUnit.MINUTES);
    }

    /**
     * 数据包格式化方法
     * 将TransferPackage对象转换为符合协议要求的字节数组
     * <p>
     * 数据包结构：
     * - SN号（序列号）
     * - 包类型（1字节）
     * - 命令编号（4字节）
     * - 时间戳（8字节）
     * - 是否补发标志（1字节）
     * - 设备类型（设备数据包时包含，1字节）
     * - 设备编码（变长）
     * - 拆包信息（快照数据包时包含，8字节）
     * - 消息内容（变长）
     *
     * @param transferPackage 要格式化的数据包
     * @return 格式化后的字节数组
     */
    private byte[] dataFormat(TransferPackage transferPackage) {
        // 计算数据包总长度：基础长度32字节 + 消息内容长度
        int length = 32 + transferPackage.getMessage().getBytes().length;

        // 根据包类型调整长度
        if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
            length++;  // 设备数据包需要额外1字节存储设备类型
        } else if (PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())) {
            length += 8;  // 快照数据包需要额外8字节存储拆包信息
        }

        // 创建字节数组
        byte[] data = new byte[length];
        int offset = 0;

        // 设置序列号，如果数据包中有SN则使用，否则使用默认值
        if (StringUtils.isNotBlank(transferPackage.getSn())) {
            sn = "Bc2cfr," + transferPackage.getSn();
        } else {
            sn = "Bc2cfr,1111111";  // 默认SN号
        }
        logger.info("授权的sn号：" + sn);

        // 按协议顺序填充数据包各字段
        // 1. SN号 + 包类型 + 命令编号 + 时间戳 + 补发标志
        offset = NumUtils.mergeBytes(data, offset, sn.getBytes());
        offset = NumUtils.mergeByte(data, offset, transferPackage.getPackageType().byteValue());
        offset = NumUtils.mergeBytes(data, offset, NumUtils.int2ByteArray(transferPackage.getCommandNum()));
        offset = NumUtils.mergeBytes(data, offset, NumUtils.longToBytes(transferPackage.getTime()));
        offset = NumUtils.mergeByte(data, offset, transferPackage.getIsRepair().byteValue());

        // 2. 如果是设备数据包，添加设备类型字段
        if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
            offset = NumUtils.mergeByte(data, offset, transferPackage.getDeviceType().byteValue());
        }

        // 3. 添加设备编码
        int offset2 = NumUtils.mergeBytes(data, offset, transferPackage.getDeviceCode().getBytes());

        // 4. 如果是快照数据包，添加拆包信息
        if (PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())) {
            offset2 = NumUtils.mergeBytes(data,
                    NumUtils.mergeBytes(data, offset2,
                            NumUtils.int2ByteArray(transferPackage.getUnpackingTotal())),
                    NumUtils.int2ByteArray(transferPackage.getUnpackingNum()));
        }

        // 5. 最后添加消息内容
        NumUtils.mergeBytes(data, offset2, transferPackage.getMessage().getBytes());

        return data;
    }

    /**
     * 清空发送参数统计
     * 重置流量控制相关的统计数据
     */
    public void cleanSendParametersMap() {
        sendParametersMap.put("hasSendLength", 0);  // 重置已发送数据长度
        sendParametersMap.put("hasTime", 0);        // 重置已用时间
    }

    /**
     * 发送连接保持消息（心跳包）
     * 定期向LAPUTA系统发送心跳包以维持连接状态
     *
     * @param udpChannel UDP通道
     */
    private void sendConnectKeepMessage(ChannelFuture udpChannel) {
        // 获取本地网络地址信息
        String localAddr = getLocalAddr();
        logger.info("ConnectKeep----ip:{}--port:{}--{}", laputaIp, laputaPort, localAddr);

        // 构造连接保持数据包
        TransferPackage transferPackage = new TransferPackage(
                1L,                                    // 命令编号
                System.currentTimeMillis(), // 当前时间戳
                PackageTypeEnum.CONNECT_KEEP.getValue(),  // 连接保持包类型
                0,                                     // 非补发
                localAddr,                             // 本地地址作为设备编码
                "sync"                                 // 同步消息内容
        );

        // 格式化数据包并发送
        byte[] udpMsg = dataFormat(transferPackage);
        udpChannel.channel().writeAndFlush(
                new DatagramPacket(Unpooled.copiedBuffer(udpMsg),
                        new InetSocketAddress(laputaIp, laputaPort)));
    }

    private void sendSyncDataSuccessMessage(ChannelFuture udpChannel, TransferPackage transferPackage) {
        byte[] udpMsg = dataFormat(transferPackage);
        udpChannel.channel().writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer(udpMsg),
                new InetSocketAddress(laputaIp, laputaPort)));
        logger.info("同步数据接收成功。。{}", JSONObject.toJSONString(transferPackage));
    }

    /**
     * 获取本地网络地址
     * 遍历所有网络接口，收集非回环地址的IP地址列表
     *
     * @return JSON格式的IP地址列表字符串
     */
    private String getLocalAddr() {
        try {
            // 获取所有网络接口
            Enumeration<NetworkInterface> enumeration = NetworkInterface.getNetworkInterfaces();
            List<String> ipv4Result = new ArrayList<>();

            // 遍历所有网络接口
            while (enumeration.hasMoreElements()) {
                NetworkInterface networkInterface = enumeration.nextElement();
                // 获取当前网络接口的所有IP地址
                Enumeration<InetAddress> en = networkInterface.getInetAddresses();

                while (en.hasMoreElements()) {
                    InetAddress address = en.nextElement();
                    // 过滤掉回环地址（如127.0.0.1），只保留真实的网络地址
                    if (!address.isLoopbackAddress()) {
                        ipv4Result.add(address.getHostAddress());
                    }
                }
            }

            // 如果找到IP地址，返回JSON格式的地址列表
            if (!ipv4Result.isEmpty()) {
                return JSONObject.toJSONString(ipv4Result);
            }
            return "";
        } catch (Exception e) {
            logger.error(e.getMessage());
            return "";
        }
    }

    private void sendRawDataMessage(ChannelFuture udpChannel, TransferPackage transferPackage, Integer cost) {
        byte[] udpMsg = dataFormat(transferPackage);
        udpChannel.channel().writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer(udpMsg),
                new InetSocketAddress(laputaIp, laputaPort)));
        logger.info("同步原始数据发送成功。。{}", JSONObject.toJSONString(transferPackage));
    }
}