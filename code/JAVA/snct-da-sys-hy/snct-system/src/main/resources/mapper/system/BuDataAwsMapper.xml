<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataAwsMapper">

    <resultMap type="BuDataAws" id="BuDataAwsResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="sn"    />
        <result property="deviceId"    column="device_id"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="utcTime"    column="utc_time"    />
        <result property="relativeWind"    column="relative_wind"    />
        <result property="relativeWindSpeed"    column="relative_wind_speed"    />
        <result property="airTemperature"    column="air_temperature"    />
        <result property="airUnit"    column="air_unit"    />
        <result property="humidity"    column="humidity"    />
        <result property="humidityUnit"    column="humidity_unit"    />
        <result property="pressure"    column="pressure"    />
        <result property="pressureUnit"    column="pressure_unit"    />
        <result property="pointTem"    column="point_tem"    />
        <result property="qfe"    column="qfe"    />
        <result property="qnh"    column="qnh"    />
        <result property="dp"    column="dp"    />
    </resultMap>

    <sql id="selectBuDataAwsVo">
        select id, sn, device_id, device_code, initial_time, initial_bj_time, `utc_time`, relative_wind, relative_wind_speed, air_temperature, air_unit, humidity, humidity_unit, pressure, pressure_unit, point_tem, qfe, qnh, dp from bu_data_aws
    </sql>

    <select id="selectBuDataAwsList" parameterType="BuDataAws" resultMap="BuDataAwsResult">
        <include refid="selectBuDataAwsVo"/>
        <where>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="params.beginInitialBjTime != null and params.beginInitialBjTime != '' and params.endInitialBjTime != null and params.endInitialBjTime != ''"> and initial_bj_time between #{params.beginInitialBjTime} and #{params.endInitialBjTime}</if>
        </where>
    </select>

    <select id="selectBuDataAwsById" parameterType="Long" resultMap="BuDataAwsResult">
        <include refid="selectBuDataAwsVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataAws" parameterType="BuDataAws" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_aws
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sn != null">sn,</if>
            <if test="deviceId != null">device_id,</if>
            <if test="deviceCode != null">device_code,</if>
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="utcTime != null">`utc_time`,</if>
            <if test="relativeWind != null">relative_wind,</if>
            <if test="relativeWindSpeed != null">relative_wind_speed,</if>
            <if test="airTemperature != null">air_temperature,</if>
            <if test="airUnit != null">air_unit,</if>
            <if test="humidity != null">humidity,</if>
            <if test="humidityUnit != null">humidity_unit,</if>
            <if test="pressure != null">pressure,</if>
            <if test="pressureUnit != null">pressure_unit,</if>
            <if test="pointTem != null">point_tem,</if>
            <if test="qfe != null">qfe,</if>
            <if test="qnh != null">qnh,</if>
            <if test="dp != null">dp,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sn != null">#{sn},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="deviceCode != null">#{deviceCode},</if>
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="utcTime != null">#{utcTime},</if>
            <if test="relativeWind != null">#{relativeWind},</if>
            <if test="relativeWindSpeed != null">#{relativeWindSpeed},</if>
            <if test="airTemperature != null">#{airTemperature},</if>
            <if test="airUnit != null">#{airUnit},</if>
            <if test="humidity != null">#{humidity},</if>
            <if test="humidityUnit != null">#{humidityUnit},</if>
            <if test="pressure != null">#{pressure},</if>
            <if test="pressureUnit != null">#{pressureUnit},</if>
            <if test="pointTem != null">#{pointTem},</if>
            <if test="qfe != null">#{qfe},</if>
            <if test="qnh != null">#{qnh},</if>
            <if test="dp != null">#{dp},</if>
        </trim>
    </insert>

    <update id="updateBuDataAws" parameterType="BuDataAws">
        update bu_data_aws
        <trim prefix="SET" suffixOverrides=",">
            <if test="sn != null">sn = #{sn},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceCode != null">device_code = #{deviceCode},</if>
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="utcTime != null">`utc_time` = #{utcTime},</if>
            <if test="relativeWind != null">relative_wind = #{relativeWind},</if>
            <if test="relativeWindSpeed != null">relative_wind_speed = #{relativeWindSpeed},</if>
            <if test="airTemperature != null">air_temperature = #{airTemperature},</if>
            <if test="airUnit != null">air_unit = #{airUnit},</if>
            <if test="humidity != null">humidity = #{humidity},</if>
            <if test="humidityUnit != null">humidity_unit = #{humidityUnit},</if>
            <if test="pressure != null">pressure = #{pressure},</if>
            <if test="pressureUnit != null">pressure_unit = #{pressureUnit},</if>
            <if test="pointTem != null">point_tem = #{pointTem},</if>
            <if test="qfe != null">qfe = #{qfe},</if>
            <if test="qnh != null">qnh = #{qnh},</if>
            <if test="dp != null">dp = #{dp},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataAwsById" parameterType="Long">
        delete from bu_data_aws where id = #{id}
    </delete>

    <delete id="deleteBuDataAwsByIds" parameterType="String">
        delete from bu_data_aws where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBuDataAwsByDate" parameterType="String">
        delete from bu_data_aws where DATE(initial_bj_time) &lt; #{cutoffDate}
    </delete>
</mapper>