package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 姿态仪数据对象 bu_data_attitude
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public class BuDataAttitude extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** SN码 */
    @Excel(name = "SN码")
    private String sn;

    /** 设备ID */
    @Excel(name = "设备ID")
    private Long deviceId;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String deviceCode;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date initialBjTime;

    /** UTC时间 */
    @Excel(name = "UTC时间")
    private String utcTime;

    /** 纬度 */
    @Excel(name = "纬度")
    private String lat;

    /** 经度 */
    @Excel(name = "经度")
    private String lon;

    /** 横摇 */
    @Excel(name = "横摇")
    private String rolling;

    /** 纵摇 */
    @Excel(name = "纵摇")
    private String pitch;

    /** 高度 */
    @Excel(name = "高度")
    private String height;

    /** 航向 */
    @Excel(name = "航向")
    private String heading;

    /** 速度 */
    @Excel(name = "速度")
    private String speed;

    /** 距离 */
    @Excel(name = "距离")
    private String distance;

    /** 站名 */
    @Excel(name = "站名")
    private String stationName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public void setDeviceId(Long deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setUtcTime(String utcTime) 
    {
        this.utcTime = utcTime;
    }

    public String getUtcTime() 
    {
        return utcTime;
    }

    public void setLat(String lat) 
    {
        this.lat = lat;
    }

    public String getLat() 
    {
        return lat;
    }

    public void setLon(String lon) 
    {
        this.lon = lon;
    }

    public String getLon() 
    {
        return lon;
    }

    public void setRolling(String rolling) 
    {
        this.rolling = rolling;
    }

    public String getRolling() 
    {
        return rolling;
    }

    public void setPitch(String pitch) 
    {
        this.pitch = pitch;
    }

    public String getPitch() 
    {
        return pitch;
    }

    public void setHeight(String height) 
    {
        this.height = height;
    }

    public String getHeight() 
    {
        return height;
    }

    public void setHeading(String heading) 
    {
        this.heading = heading;
    }

    public String getHeading() 
    {
        return heading;
    }

    public void setSpeed(String speed) 
    {
        this.speed = speed;
    }

    public String getSpeed() 
    {
        return speed;
    }

    public void setDistance(String distance) 
    {
        this.distance = distance;
    }

    public String getDistance() 
    {
        return distance;
    }

    public void setStationName(String stationName) 
    {
        this.stationName = stationName;
    }

    public String getStationName() 
    {
        return stationName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sn", getSn())
            .append("deviceId", getDeviceId())
            .append("deviceCode", getDeviceCode())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("utcTime", getUtcTime())
            .append("lat", getLat())
            .append("lon", getLon())
            .append("rolling", getRolling())
            .append("pitch", getPitch())
            .append("height", getHeight())
            .append("heading", getHeading())
            .append("speed", getSpeed())
            .append("distance", getDistance())
            .append("stationName", getStationName())
            .toString();
    }
}
