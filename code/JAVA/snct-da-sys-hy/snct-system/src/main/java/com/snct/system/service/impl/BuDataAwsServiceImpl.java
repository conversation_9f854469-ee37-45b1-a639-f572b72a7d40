package com.snct.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataAwsMapper;
import com.snct.system.domain.data.BuDataAws;
import com.snct.system.service.IBuDataAwsService;

/**
 * AWS气象数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
public class BuDataAwsServiceImpl implements IBuDataAwsService 
{
    @Autowired
    private BuDataAwsMapper buDataAwsMapper;

    /**
     * 查询AWS气象数据
     * 
     * @param id AWS气象数据主键
     * @return AWS气象数据
     */
    @Override
    public BuDataAws selectBuDataAwsById(Long id)
    {
        return buDataAwsMapper.selectBuDataAwsById(id);
    }

    /**
     * 查询AWS气象数据列表
     * 
     * @param buDataAws AWS气象数据
     * @return AWS气象数据
     */
    @Override
    public List<BuDataAws> selectBuDataAwsList(BuDataAws buDataAws)
    {
        return buDataAwsMapper.selectBuDataAwsList(buDataAws);
    }

    /**
     * 新增AWS气象数据
     * 
     * @param buDataAws AWS气象数据
     * @return 结果
     */
    @Override
    public int insertBuDataAws(BuDataAws buDataAws)
    {
        return buDataAwsMapper.insertBuDataAws(buDataAws);
    }

    /**
     * 修改AWS气象数据
     * 
     * @param buDataAws AWS气象数据
     * @return 结果
     */
    @Override
    public int updateBuDataAws(BuDataAws buDataAws)
    {
        return buDataAwsMapper.updateBuDataAws(buDataAws);
    }

    /**
     * 批量删除AWS气象数据
     * 
     * @param ids 需要删除的AWS气象数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAwsByIds(Long[] ids)
    {
        return buDataAwsMapper.deleteBuDataAwsByIds(ids);
    }

    /**
     * 删除AWS气象数据信息
     * 
     * @param id AWS气象数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAwsById(Long id)
    {
        return buDataAwsMapper.deleteBuDataAwsById(id);
    }
}
