package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.data.BuDataModem;

/**
 * 卫星猫数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface IBuDataModemService 
{
    /**
     * 查询卫星猫数据
     * 
     * @param id 卫星猫数据主键
     * @return 卫星猫数据
     */
    public BuDataModem selectBuDataModemById(Long id);

    /**
     * 查询卫星猫数据列表
     * 
     * @param buDataModem 卫星猫数据
     * @return 卫星猫数据集合
     */
    public List<BuDataModem> selectBuDataModemList(BuDataModem buDataModem);

    /**
     * 新增卫星猫数据
     * 
     * @param buDataModem 卫星猫数据
     * @return 结果
     */
    public int insertBuDataModem(BuDataModem buDataModem);

    /**
     * 修改卫星猫数据
     * 
     * @param buDataModem 卫星猫数据
     * @return 结果
     */
    public int updateBuDataModem(BuDataModem buDataModem);

    /**
     * 批量删除卫星猫数据
     * 
     * @param ids 需要删除的卫星猫数据主键集合
     * @return 结果
     */
    public int deleteBuDataModemByIds(Long[] ids);

    /**
     * 删除卫星猫数据信息
     * 
     * @param id 卫星猫数据主键
     * @return 结果
     */
    public int deleteBuDataModemById(Long id);
}
