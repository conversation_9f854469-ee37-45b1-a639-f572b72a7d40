package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.data.BuDataGps;

/**
 * GPS数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface IBuDataGpsService 
{
    /**
     * 查询GPS数据
     * 
     * @param id GPS数据主键
     * @return GPS数据
     */
    public BuDataGps selectBuDataGpsById(Long id);

    /**
     * 查询GPS数据列表
     * 
     * @param buDataGps GPS数据
     * @return GPS数据集合
     */
    public List<BuDataGps> selectBuDataGpsList(BuDataGps buDataGps);

    /**
     * 新增GPS数据
     * 
     * @param buDataGps GPS数据
     * @return 结果
     */
    public int insertBuDataGps(BuDataGps buDataGps);

    /**
     * 修改GPS数据
     * 
     * @param buDataGps GPS数据
     * @return 结果
     */
    public int updateBuDataGps(BuDataGps buDataGps);

    /**
     * 批量删除GPS数据
     * 
     * @param ids 需要删除的GPS数据主键集合
     * @return 结果
     */
    public int deleteBuDataGpsByIds(Long[] ids);

    /**
     * 删除GPS数据信息
     * 
     * @param id GPS数据主键
     * @return 结果
     */
    public int deleteBuDataGpsById(Long id);
}
