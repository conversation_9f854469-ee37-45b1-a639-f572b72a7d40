package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.data.BuDataAttitude;

/**
 * 姿态仪数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public interface IBuDataAttitudeService 
{
    /**
     * 查询姿态仪数据
     * 
     * @param id 姿态仪数据主键
     * @return 姿态仪数据
     */
    public BuDataAttitude selectBuDataAttitudeById(Long id);

    /**
     * 查询姿态仪数据列表
     * 
     * @param buDataAttitude 姿态仪数据
     * @return 姿态仪数据集合
     */
    public List<BuDataAttitude> selectBuDataAttitudeList(BuDataAttitude buDataAttitude);

    /**
     * 新增姿态仪数据
     * 
     * @param buDataAttitude 姿态仪数据
     * @return 结果
     */
    public int insertBuDataAttitude(BuDataAttitude buDataAttitude);

    /**
     * 修改姿态仪数据
     * 
     * @param buDataAttitude 姿态仪数据
     * @return 结果
     */
    public int updateBuDataAttitude(BuDataAttitude buDataAttitude);

    /**
     * 批量删除姿态仪数据
     * 
     * @param ids 需要删除的姿态仪数据主键集合
     * @return 结果
     */
    public int deleteBuDataAttitudeByIds(Long[] ids);

    /**
     * 删除姿态仪数据信息
     * 
     * @param id 姿态仪数据主键
     * @return 结果
     */
    public int deleteBuDataAttitudeById(Long id);
}
