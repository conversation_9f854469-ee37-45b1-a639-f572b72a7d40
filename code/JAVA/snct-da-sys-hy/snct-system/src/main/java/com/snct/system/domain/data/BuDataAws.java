package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * AWS气象数据对象 bu_data_aws
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
public class BuDataAws extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** SN码 */
    @Excel(name = "SN码")
    private String sn;

    /** 设备ID */
    @Excel(name = "设备ID")
    private Long deviceId;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String deviceCode;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date initialBjTime;

    /** UTC时间 */
    @Excel(name = "UTC时间")
    private String utcTime;

    /** 相对风向 */
    @Excel(name = "相对风向")
    private String relativeWind;

    /** 相对风速 */
    @Excel(name = "相对风速")
    private String relativeWindSpeed;

    /** 气温值 */
    @Excel(name = "气温值")
    private String airTemperature;

    /** 气温单位 */
    @Excel(name = "气温单位")
    private String airUnit;

    /** 相对湿度数值 */
    @Excel(name = "相对湿度数值")
    private String humidity;

    /** 相对湿度单位 */
    @Excel(name = "相对湿度单位")
    private String humidityUnit;

    /** 气压数值 */
    @Excel(name = "气压数值")
    private String pressure;

    /** 气压单位 */
    @Excel(name = "气压单位")
    private String pressureUnit;

    /** 露点温度值 */
    @Excel(name = "露点温度值")
    private String pointTem;

    /** 场面气压数值 */
    @Excel(name = "场面气压数值")
    private String qfe;

    /** 海平面气压数值 */
    @Excel(name = "海平面气压数值")
    private String qnh;

    /** 露点 */
    @Excel(name = "露点")
    private String dp;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public void setDeviceId(Long deviceId) 
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceCode(String deviceCode) 
    {
        this.deviceCode = deviceCode;
    }

    public String getDeviceCode() 
    {
        return deviceCode;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setUtcTime(String utcTime) 
    {
        this.utcTime = utcTime;
    }

    public String getUtcTime() 
    {
        return utcTime;
    }

    public void setRelativeWind(String relativeWind) 
    {
        this.relativeWind = relativeWind;
    }

    public String getRelativeWind() 
    {
        return relativeWind;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) 
    {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getRelativeWindSpeed() 
    {
        return relativeWindSpeed;
    }

    public void setAirTemperature(String airTemperature) 
    {
        this.airTemperature = airTemperature;
    }

    public String getAirTemperature() 
    {
        return airTemperature;
    }

    public void setAirUnit(String airUnit) 
    {
        this.airUnit = airUnit;
    }

    public String getAirUnit() 
    {
        return airUnit;
    }

    public void setHumidity(String humidity) 
    {
        this.humidity = humidity;
    }

    public String getHumidity() 
    {
        return humidity;
    }

    public void setHumidityUnit(String humidityUnit) 
    {
        this.humidityUnit = humidityUnit;
    }

    public String getHumidityUnit() 
    {
        return humidityUnit;
    }

    public void setPressure(String pressure) 
    {
        this.pressure = pressure;
    }

    public String getPressure() 
    {
        return pressure;
    }

    public void setPressureUnit(String pressureUnit) 
    {
        this.pressureUnit = pressureUnit;
    }

    public String getPressureUnit() 
    {
        return pressureUnit;
    }

    public void setPointTem(String pointTem) 
    {
        this.pointTem = pointTem;
    }

    public String getPointTem() 
    {
        return pointTem;
    }

    public void setQfe(String qfe) 
    {
        this.qfe = qfe;
    }

    public String getQfe() 
    {
        return qfe;
    }

    public void setQnh(String qnh) 
    {
        this.qnh = qnh;
    }

    public String getQnh() 
    {
        return qnh;
    }

    public void setDp(String dp) 
    {
        this.dp = dp;
    }

    public String getDp() 
    {
        return dp;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sn", getSn())
            .append("deviceId", getDeviceId())
            .append("deviceCode", getDeviceCode())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("utcTime", getUtcTime())
            .append("relativeWind", getRelativeWind())
            .append("relativeWindSpeed", getRelativeWindSpeed())
            .append("airTemperature", getAirTemperature())
            .append("airUnit", getAirUnit())
            .append("humidity", getHumidity())
            .append("humidityUnit", getHumidityUnit())
            .append("pressure", getPressure())
            .append("pressureUnit", getPressureUnit())
            .append("pointTem", getPointTem())
            .append("qfe", getQfe())
            .append("qnh", getQnh())
            .append("dp", getDp())
            .toString();
    }
}
