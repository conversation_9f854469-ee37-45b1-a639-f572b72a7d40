package com.snct.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataGpsMapper;
import com.snct.system.domain.data.BuDataGps;
import com.snct.system.service.IBuDataGpsService;

/**
 * GPS数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-09
 */
@Service
public class BuDataGpsServiceImpl implements IBuDataGpsService 
{
    @Autowired
    private BuDataGpsMapper buDataGpsMapper;

    /**
     * 查询GPS数据
     * 
     * @param id GPS数据主键
     * @return GPS数据
     */
    @Override
    public BuDataGps selectBuDataGpsById(Long id)
    {
        return buDataGpsMapper.selectBuDataGpsById(id);
    }

    /**
     * 查询GPS数据列表
     * 
     * @param buDataGps GPS数据
     * @return GPS数据
     */
    @Override
    public List<BuDataGps> selectBuDataGpsList(BuDataGps buDataGps)
    {
        return buDataGpsMapper.selectBuDataGpsList(buDataGps);
    }

    /**
     * 新增GPS数据
     * 
     * @param buDataGps GPS数据
     * @return 结果
     */
    @Override
    public int insertBuDataGps(BuDataGps buDataGps)
    {
        return buDataGpsMapper.insertBuDataGps(buDataGps);
    }

    /**
     * 修改GPS数据
     * 
     * @param buDataGps GPS数据
     * @return 结果
     */
    @Override
    public int updateBuDataGps(BuDataGps buDataGps)
    {
        return buDataGpsMapper.updateBuDataGps(buDataGps);
    }

    /**
     * 批量删除GPS数据
     * 
     * @param ids 需要删除的GPS数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataGpsByIds(Long[] ids)
    {
        return buDataGpsMapper.deleteBuDataGpsByIds(ids);
    }

    /**
     * 删除GPS数据信息
     * 
     * @param id GPS数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataGpsById(Long id)
    {
        return buDataGpsMapper.deleteBuDataGpsById(id);
    }
}
